package com.cleevio.cinemax.api.module.card.event.listener

import com.cleevio.cinemax.api.module.basket.service.command.UseCardsOnBasketCommand
import com.cleevio.cinemax.api.module.card.service.BasketCinemaxCardsService
import com.cleevio.cinemax.api.module.card.service.CreateCardOrderService
import com.cleevio.cinemax.api.module.card.service.command.CreateCardOrderCommand
import com.cleevio.cinemax.api.module.table.event.BasketPaidEvent
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    "features.basket.card.listener.enabled",
    havingValue = "true",
    matchIfMissing = true
)
class BasketCardEventListener(
    private val basketCinemaxCardsService: BasketCinemaxCardsService,
    private val createCardOrderService: CreateCardOrderService,
) {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToBasketPaidEvent(event: BasketPaidEvent) {
        basketCinemaxCardsService.useCardsOnBasket(
            UseCardsOnBasketCommand(
                basketId = event.basketId,
                posType = event.posType
            )
        )

        createCardOrderService.createOrder(
            CreateCardOrderCommand(
                basketId = event.basketId
            )
        )
    }
}
